#include "ti_msp_dl_config.h"
#include "pwm.h"

void Motor_Init()
{
    DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);//AIN1
    DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);//AIN2
    DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT, GPIO_IN_PIN_INB1_PIN);//BIN1
    DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT, GPIO_IN_PIN_INB2_PIN);//BIN2
    DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT, GPIO_IN_PIN_INC1_PIN);//CIN1
    DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT, GPIO_IN_PIN_INC2_PIN);//CIN2
    DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT, GPIO_IN_PIN_IND1_PIN);//DIN1
    DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT, GPIO_IN_PIN_IND2_PIN);//DIN2
}

void Motor_GO()
{
    //DL_GPIO_setPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutyrr==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT, GPIO_IN_PIN_INC1_PIN);//CIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT, GPIO_IN_PIN_INC2_PIN);//CIN2
    }
    else
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
        DL_GPIO_setPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    }
    if(Dutyrf==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT, GPIO_IN_PIN_IND1_PIN);//DIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT, GPIO_IN_PIN_IND2_PIN);//DIN2
    }
    else
    {
        DL_GPIO_setPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
        DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    }
    if(Dutylf==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);//AIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);//AIN2
    }
    else
    {
        DL_GPIO_setPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
        DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    }
    if(Dutylr==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT, GPIO_IN_PIN_INB1_PIN);//BIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT, GPIO_IN_PIN_INB2_PIN);//BIN2
    }
    else 
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
        DL_GPIO_setPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    }
}

void Motor_BACK()
{
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutyrr==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT, GPIO_IN_PIN_INC1_PIN);//CIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT, GPIO_IN_PIN_INC2_PIN);//CIN2
    }
    else
    {
        DL_GPIO_setPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
        DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    }
    if(Dutyrf==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT, GPIO_IN_PIN_IND1_PIN);//DIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT, GPIO_IN_PIN_IND2_PIN);//DIN2
    }
    else
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
        DL_GPIO_setPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    }
    if(Dutylf==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);//AIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);//AIN2
    }
    else
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
        DL_GPIO_setPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    }
    if(Dutylr==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT, GPIO_IN_PIN_INB1_PIN);//BIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT, GPIO_IN_PIN_INB2_PIN);//BIN2
    }
    else
    {
        DL_GPIO_setPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
        DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    }
}

void Motor_RF_GO()
{
    //DL_GPIO_setPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);

    if(Dutyrf==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT, GPIO_IN_PIN_IND1_PIN);//DIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT, GPIO_IN_PIN_IND2_PIN);//DIN2
    }
    else
    {
        DL_GPIO_setPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
        DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    }
}

void Motor_RR_GO()
{
    //DL_GPIO_setPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutyrr==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT, GPIO_IN_PIN_INC1_PIN);//CIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT, GPIO_IN_PIN_INC2_PIN);//CIN2
    }
    else
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
        DL_GPIO_setPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    }
}

void Motor_LF_GO()
{
    //DL_GPIO_setPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutylf==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);//AIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);//AIN2
    }
    else
    {
        DL_GPIO_setPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
        DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    }
}

void Motor_LR_GO()
{
    //DL_GPIO_setPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutylr==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT, GPIO_IN_PIN_INB1_PIN);//BIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT, GPIO_IN_PIN_INB2_PIN);//BIN2
    }
    else 
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
        DL_GPIO_setPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    }
}

void Motor_RF_BACK()
{
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutyrf==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT, GPIO_IN_PIN_IND1_PIN);//DIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_IND2_PORT, GPIO_IN_PIN_IND2_PIN);//DIN2
    }
    else
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
        DL_GPIO_setPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    }
}

void Motor_RR_BACK()
{
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutyrr==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INC1_PORT, GPIO_IN_PIN_INC1_PIN);//CIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT, GPIO_IN_PIN_INC2_PIN);//CIN2
    }
    else
    {
        DL_GPIO_setPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
        DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    }
}

void Motor_LF_BACK()
{
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutylf==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);//AIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);//AIN2
    }
    else
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
        DL_GPIO_setPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    }
}

void Motor_LR_BACK()
{
    //DL_GPIO_clearPins(GPIO_IN_PIN_INA1_PORT, GPIO_IN_PIN_INA1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INA2_PORT, GPIO_IN_PIN_INA2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_INC1_PORT,GPIO_IN_PIN_INC1_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_INC2_PORT,GPIO_IN_PIN_INC2_PIN);
    //DL_GPIO_clearPins(GPIO_IN_PIN_IND1_PORT,GPIO_IN_PIN_IND1_PIN);
    //DL_GPIO_setPins(GPIO_IN_PIN_IND2_PORT,GPIO_IN_PIN_IND2_PIN);
    if(Dutylr==0)
    {
        DL_GPIO_clearPins(GPIO_IN_PIN_INB1_PORT, GPIO_IN_PIN_INB1_PIN);//BIN1
        DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT, GPIO_IN_PIN_INB2_PIN);//BIN2
    }
    else
    {
        DL_GPIO_setPins(GPIO_IN_PIN_INB1_PORT,GPIO_IN_PIN_INB1_PIN);
        DL_GPIO_clearPins(GPIO_IN_PIN_INB2_PORT,GPIO_IN_PIN_INB2_PIN);
    }
}