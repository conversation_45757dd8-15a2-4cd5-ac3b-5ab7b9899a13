#include "ti_msp_dl_config.h"

float Dutyrr,Dutyrf,Dutylf,Dutylr;

void PWM_SET_Init()
{
    DL_Timer_startCounter(PWM_RIGHT_INST);
    DL_Timer_startCounter(PWM_LEFT_INST);
}

void PWM_SET_DutyRR(float DutyRR)
{
    uint32_t CompareRR;
    Dutyrr=DutyRR;
    CompareRR=320-320*DutyRR/1000;
    DL_TimerA_setCaptureCompareValue(PWM_RIGHT_INST, CompareRR, DL_TIMER_CC_0_INDEX);
}

void PWM_SET_DutyRF(float DutyRF)
{
    uint32_t CompareRF;
    Dutyrf=DutyRF;
    CompareRF=320-320*DutyRF/1000;
    DL_TimerA_setCaptureCompareValue(PWM_RIGHT_INST, CompareRF, DL_TIMER_CC_1_INDEX);
}

void PWM_SET_DutyLF(float DutyLF)
{
    uint32_t CompareLF;
    Dutylf=DutyLF;
    CompareLF=320-320*DutyLF/1000;
    DL_TimerG_setCaptureCompareValue(PWM_LEFT_INST, CompareLF, DL_TIMER_CC_0_INDEX);
}

void PWM_SET_DutyLR(float DutyLR)
{
    uint32_t CompareLR;
    Dutylr=DutyLR;
    CompareLR=320-320*DutyLR/1000;
    DL_TimerG_setCaptureCompareValue(PWM_LEFT_INST, CompareLR, DL_TIMER_CC_1_INDEX);
}