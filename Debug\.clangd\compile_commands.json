[{"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay/dealy.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050/inv_mpu.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050/inv_mpu_dmp_motion_driver.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050/mpu6050.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050/mspm0_i2c.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0/clock.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0/interrupt.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING/eight_tracking.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR/motor.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM/pwm.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/LSM6DSV16X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/VL53L0X\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/WIT\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/BNO08X_UART_RVC\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_GPIO\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/Ultrasonic_Capture\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Hardware_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_I2C\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/OLED_Software_SPI\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MPU6050\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/PWM\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/EIGHT_TRACKING\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Delay\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Debug\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_05_00_05/source\" -I\"C:/Users/<USER>/workspace_ccstheia/mspm0-modules/Drivers/MSPM0\" -DMOTION_DRIVER_TARGET_MSPM0 -DMPU6050 -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c++/v1\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib/clang/18/include\" -isystem\"C:/ti/ccs2020/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/mspm0-modules/main.c"}]