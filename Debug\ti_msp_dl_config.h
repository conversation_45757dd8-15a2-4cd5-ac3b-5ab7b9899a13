/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_RIGHT */
#define PWM_RIGHT_INST                                                     TIMA1
#define PWM_RIGHT_INST_IRQHandler                               TIMA1_IRQHandler
#define PWM_RIGHT_INST_INT_IRQN                                 (TIMA1_INT_IRQn)
#define PWM_RIGHT_INST_CLK_FREQ                                         32000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_RIGHT_C0_PORT                                             GPIOB
#define GPIO_PWM_RIGHT_C0_PIN                                      DL_GPIO_PIN_4
#define GPIO_PWM_RIGHT_C0_IOMUX                                  (IOMUX_PINCM17)
#define GPIO_PWM_RIGHT_C0_IOMUX_FUNC                 IOMUX_PINCM17_PF_TIMA1_CCP0
#define GPIO_PWM_RIGHT_C0_IDX                                DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_RIGHT_C1_PORT                                             GPIOA
#define GPIO_PWM_RIGHT_C1_PIN                                     DL_GPIO_PIN_16
#define GPIO_PWM_RIGHT_C1_IOMUX                                  (IOMUX_PINCM38)
#define GPIO_PWM_RIGHT_C1_IOMUX_FUNC                 IOMUX_PINCM38_PF_TIMA1_CCP1
#define GPIO_PWM_RIGHT_C1_IDX                                DL_TIMER_CC_1_INDEX

/* Defines for PWM_LEFT */
#define PWM_LEFT_INST                                                     TIMG12
#define PWM_LEFT_INST_IRQHandler                               TIMG12_IRQHandler
#define PWM_LEFT_INST_INT_IRQN                                 (TIMG12_INT_IRQn)
#define PWM_LEFT_INST_CLK_FREQ                                          32000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_LEFT_C0_PORT                                              GPIOB
#define GPIO_PWM_LEFT_C0_PIN                                      DL_GPIO_PIN_13
#define GPIO_PWM_LEFT_C0_IOMUX                                   (IOMUX_PINCM30)
#define GPIO_PWM_LEFT_C0_IOMUX_FUNC                 IOMUX_PINCM30_PF_TIMG12_CCP0
#define GPIO_PWM_LEFT_C0_IDX                                 DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWM_LEFT_C1_PORT                                              GPIOB
#define GPIO_PWM_LEFT_C1_PIN                                      DL_GPIO_PIN_24
#define GPIO_PWM_LEFT_C1_IOMUX                                   (IOMUX_PINCM52)
#define GPIO_PWM_LEFT_C1_IOMUX_FUNC                 IOMUX_PINCM52_PF_TIMG12_CCP1
#define GPIO_PWM_LEFT_C1_IDX                                 DL_TIMER_CC_1_INDEX



/* Defines for TIMER_0 */
#define TIMER_0_INST                                                     (TIMA0)
#define TIMER_0_INST_IRQHandler                                 TIMA0_IRQHandler
#define TIMER_0_INST_INT_IRQN                                   (TIMA0_INT_IRQn)
#define TIMER_0_INST_LOAD_VALUE                                           (624U)




/* Defines for I2C_MPU6050 */
#define I2C_MPU6050_INST                                                    I2C0
#define I2C_MPU6050_INST_IRQHandler                              I2C0_IRQHandler
#define I2C_MPU6050_INST_INT_IRQN                                  I2C0_INT_IRQn
#define I2C_MPU6050_BUS_SPEED_HZ                                          400000
#define GPIO_I2C_MPU6050_SDA_PORT                                          GPIOA
#define GPIO_I2C_MPU6050_SDA_PIN                                  DL_GPIO_PIN_28
#define GPIO_I2C_MPU6050_IOMUX_SDA                                (IOMUX_PINCM3)
#define GPIO_I2C_MPU6050_IOMUX_SDA_FUNC                 IOMUX_PINCM3_PF_I2C0_SDA
#define GPIO_I2C_MPU6050_SCL_PORT                                          GPIOA
#define GPIO_I2C_MPU6050_SCL_PIN                                  DL_GPIO_PIN_31
#define GPIO_I2C_MPU6050_IOMUX_SCL                                (IOMUX_PINCM6)
#define GPIO_I2C_MPU6050_IOMUX_SCL_FUNC                 IOMUX_PINCM6_PF_I2C0_SCL


/* Defines for UART_0 */
#define UART_0_INST                                                        UART1
#define UART_0_INST_FREQUENCY                                           32000000
#define UART_0_INST_IRQHandler                                  UART1_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART1_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                         DL_GPIO_PIN_9
#define GPIO_UART_0_TX_PIN                                         DL_GPIO_PIN_8
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM20)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM19)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM20_PF_UART1_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM19_PF_UART1_TX
#define UART_0_BAUD_RATE                                                  (9600)
#define UART_0_IBRD_32_MHZ_9600_BAUD                                       (208)
#define UART_0_FBRD_32_MHZ_9600_BAUD                                        (21)





/* Port definition for Pin Group GPIO_MPU6050 */
#define GPIO_MPU6050_PORT                                                (GPIOB)

/* Defines for PIN_INT: GPIOB.1 with pinCMx 13 on package pin 48 */
// pins affected by this interrupt request:["PIN_INT"]
#define GPIO_MPU6050_INT_IRQN                                   (GPIOB_INT_IRQn)
#define GPIO_MPU6050_INT_IIDX                   (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define GPIO_MPU6050_PIN_INT_IIDX                            (DL_GPIO_IIDX_DIO1)
#define GPIO_MPU6050_PIN_INT_PIN                                 (DL_GPIO_PIN_1)
#define GPIO_MPU6050_PIN_INT_IOMUX                               (IOMUX_PINCM13)
/* Defines for PIN_INA1: GPIOB.15 with pinCMx 32 on package pin 3 */
#define GPIO_IN_PIN_INA1_PORT                                            (GPIOB)
#define GPIO_IN_PIN_INA1_PIN                                    (DL_GPIO_PIN_15)
#define GPIO_IN_PIN_INA1_IOMUX                                   (IOMUX_PINCM32)
/* Defines for PIN_INA2: GPIOB.16 with pinCMx 33 on package pin 4 */
#define GPIO_IN_PIN_INA2_PORT                                            (GPIOB)
#define GPIO_IN_PIN_INA2_PIN                                    (DL_GPIO_PIN_16)
#define GPIO_IN_PIN_INA2_IOMUX                                   (IOMUX_PINCM33)
/* Defines for PIN_INB1: GPIOA.27 with pinCMx 60 on package pin 31 */
#define GPIO_IN_PIN_INB1_PORT                                            (GPIOA)
#define GPIO_IN_PIN_INB1_PIN                                    (DL_GPIO_PIN_27)
#define GPIO_IN_PIN_INB1_IOMUX                                   (IOMUX_PINCM60)
/* Defines for PIN_INB2: GPIOB.19 with pinCMx 45 on package pin 16 */
#define GPIO_IN_PIN_INB2_PORT                                            (GPIOB)
#define GPIO_IN_PIN_INB2_PIN                                    (DL_GPIO_PIN_19)
#define GPIO_IN_PIN_INB2_IOMUX                                   (IOMUX_PINCM45)
/* Defines for PIN_INC1: GPIOA.22 with pinCMx 47 on package pin 18 */
#define GPIO_IN_PIN_INC1_PORT                                            (GPIOA)
#define GPIO_IN_PIN_INC1_PIN                                    (DL_GPIO_PIN_22)
#define GPIO_IN_PIN_INC1_IOMUX                                   (IOMUX_PINCM47)
/* Defines for PIN_INC2: GPIOB.18 with pinCMx 44 on package pin 15 */
#define GPIO_IN_PIN_INC2_PORT                                            (GPIOB)
#define GPIO_IN_PIN_INC2_PIN                                    (DL_GPIO_PIN_18)
#define GPIO_IN_PIN_INC2_IOMUX                                   (IOMUX_PINCM44)
/* Defines for PIN_IND1: GPIOA.18 with pinCMx 40 on package pin 11 */
#define GPIO_IN_PIN_IND1_PORT                                            (GPIOA)
#define GPIO_IN_PIN_IND1_PIN                                    (DL_GPIO_PIN_18)
#define GPIO_IN_PIN_IND1_IOMUX                                   (IOMUX_PINCM40)
/* Defines for PIN_IND2: GPIOA.24 with pinCMx 54 on package pin 25 */
#define GPIO_IN_PIN_IND2_PORT                                            (GPIOA)
#define GPIO_IN_PIN_IND2_PIN                                    (DL_GPIO_PIN_24)
#define GPIO_IN_PIN_IND2_IOMUX                                   (IOMUX_PINCM54)
/* Defines for OUT1: GPIOA.17 with pinCMx 39 on package pin 10 */
#define GPIO_GRP_huidu_OUT1_PORT                                         (GPIOA)
#define GPIO_GRP_huidu_OUT1_PIN                                 (DL_GPIO_PIN_17)
#define GPIO_GRP_huidu_OUT1_IOMUX                                (IOMUX_PINCM39)
/* Defines for OUT2: GPIOA.15 with pinCMx 37 on package pin 8 */
#define GPIO_GRP_huidu_OUT2_PORT                                         (GPIOA)
#define GPIO_GRP_huidu_OUT2_PIN                                 (DL_GPIO_PIN_15)
#define GPIO_GRP_huidu_OUT2_IOMUX                                (IOMUX_PINCM37)
/* Defines for OUT3: GPIOA.12 with pinCMx 34 on package pin 5 */
#define GPIO_GRP_huidu_OUT3_PORT                                         (GPIOA)
#define GPIO_GRP_huidu_OUT3_PIN                                 (DL_GPIO_PIN_12)
#define GPIO_GRP_huidu_OUT3_IOMUX                                (IOMUX_PINCM34)
/* Defines for OUT4: GPIOA.13 with pinCMx 35 on package pin 6 */
#define GPIO_GRP_huidu_OUT4_PORT                                         (GPIOA)
#define GPIO_GRP_huidu_OUT4_PIN                                 (DL_GPIO_PIN_13)
#define GPIO_GRP_huidu_OUT4_IOMUX                                (IOMUX_PINCM35)
/* Defines for OUT5: GPIOB.8 with pinCMx 25 on package pin 60 */
#define GPIO_GRP_huidu_OUT5_PORT                                         (GPIOB)
#define GPIO_GRP_huidu_OUT5_PIN                                  (DL_GPIO_PIN_8)
#define GPIO_GRP_huidu_OUT5_IOMUX                                (IOMUX_PINCM25)
/* Defines for OUT6: GPIOB.7 with pinCMx 24 on package pin 59 */
#define GPIO_GRP_huidu_OUT6_PORT                                         (GPIOB)
#define GPIO_GRP_huidu_OUT6_PIN                                  (DL_GPIO_PIN_7)
#define GPIO_GRP_huidu_OUT6_IOMUX                                (IOMUX_PINCM24)
/* Defines for OUT7: GPIOB.17 with pinCMx 43 on package pin 14 */
#define GPIO_GRP_huidu_OUT7_PORT                                         (GPIOB)
#define GPIO_GRP_huidu_OUT7_PIN                                 (DL_GPIO_PIN_17)
#define GPIO_GRP_huidu_OUT7_IOMUX                                (IOMUX_PINCM43)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_RIGHT_init(void);
void SYSCFG_DL_PWM_LEFT_init(void);
void SYSCFG_DL_TIMER_0_init(void);
void SYSCFG_DL_I2C_MPU6050_init(void);
void SYSCFG_DL_UART_0_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
