#include "eight_tracking.h"
#include "pwm.h"
#include "motor.h"
#include "delay.h"
#include "mpu6050.h"

#define IRTrack_Trun_KP (5)
#define IRTrack_Trun_KI (0) 
#define IRTrack_Trun_KD (2) 
#define quanshu   1
#define IRR_SPEED 			  300  //Ѳ���ٶ�   Patrol speed

int pid_output_IRR = 0;
u8 trun_flag = 0;
u8 runsign=0;
u8 stopsign=0;
u8 BZW=0;
u8 x1,x2,x3,x4,x5,x6,x7; 

float PID_IR_Calc(int8_t actual_value)
{

	float IRTrackTurn = 0;
	int8_t error;
	static int8_t error_last=0;
	static float IRTrack_Integral;

	error=actual_value;
	
	IRTrack_Integral +=error;
	
	//λ��ʽpid    Positional pid
	IRTrackTurn=error*IRTrack_Trun_KP
							+IRTrack_Trun_KI*IRTrack_Integral
							+(error - error_last)*IRTrack_Trun_KD;
	return IRTrackTurn;
}

//��ȡ��·Ѳ��ģ��������   Getting data from the 8-way patrol module
/*void deal_IRdata(u8 *x1,u8 *x2,u8 *x3,u8 *x4,u8 *x5,u8 *x6,u8 *x7,u8 *x8)
{
	u8 IRbuf = 0xFF;
	IRbuf = IRI2C_ReadByte(0x30);
	
	*x1 = (IRbuf>>7)&0x01;
	*x2 = (IRbuf>>6)&0x01;
	*x3 = (IRbuf>>5)&0x01;
	*x4 = (IRbuf>>4)&0x01;
	*x5 = (IRbuf>>3)&0x01;
	*x6 = (IRbuf>>2)&0x01;
	*x7 = (IRbuf>>1)&0x01;
	*x8 = (IRbuf>>0)&0x01;
}*/


/*void printf_i2c_data(void)
{
    static uint8_t ir_x1,ir_x2,ir_x3,ir_x4,ir_x5,ir_x6,ir_x7,ir_x8;
    deal_IRdata(&ir_x1,&ir_x2,&ir_x3,&ir_x4,&ir_x5,&ir_x6,&ir_x7,&ir_x8);
    printf("x1:%d,x2:%d,x3:%d,x4:%d,x5:%d,x6:%d,x7:%d,x8:%d\r\n",ir_x1,ir_x2,ir_x3,ir_x4,ir_x5,ir_x6,ir_x7,ir_x8);
}*/

void LineWalking(void)
{
	static int8_t err = 0;


	//deal_IRdata(&x1,&x2,&x3,&x4,&x5,&x6,&x7,&x8);
	
	///L1ΪX1���׵�����ʱΪ1��������ʱΪ0     L1 is X1, 1 when the white background is off, 0 when the black line is on///
	
//�����ж�  Priority judgment
    //1100 0011
	if(x1 == 1 && x2 == 1 &&x3 == 0 &&  x4 == 0  && x5 == 0 && x6  == 1&& x7 == 1) //������   transverse acute angle
	{
		err = 15; 
	}
	else if(x1 == 1 && x2 == 1 &&x3 == 1 &&  x4 == 1  && x5 == 1 && x6  == 1 && x7 == 1 ) //������  transverse acute angle
	{
		if(trun_flag == 0) //������    out of the line
		{
			err = 15; 
			trun_flag = 1;
		}
		//�������������ϸ�״̬    Otherwise, the situation remains the same as before.
	}

//�����ж��Ƿ���ֱ�ǻ�����  Prioritize whether to right angles or acute angles
	 if(x1 == 0 && x2 == 0  && x3 == 0&& x4 == 0 && x5 == 1 && x6 == 1  && x7 == 1) // 0000 0111
	{
		err = -15;
        delay_ms(100);
	}
    else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 0 && x5 == 0 && x6 == 0  && x7 == 0) // 1110 0000
	{
		err = 15;
        delay_ms(100);
	}

  else if(x1 == 0  && x7 == 0) //���߶�����ֱ��    Both sides are lit. Run straight.
	{
		err = 0;
		if(trun_flag == 1)
		{
			trun_flag = 0;//�ߵ�Ȧ��    Walking in circles.
		}
	}
	
 else if(x1 == 0 &&  x3 == 0 && x4 == 0 && x5 == 0 && x7 == 0 )
	{
		err = 0;
	}
	//����ֱ��  Add Right Angle
	else if((x1 == 0 || x2 == 0 ) && x7 == 1) 
	{
		err = 15; 
	}
	//����ֱ��  Add Right Angle
	else if((x6 == 0 ||  x7 == 0) && x1 == 1) 
	{
		err = -15 ;
	}
	

	else if(x1 == 1 && x2 == 1  && x3 == 0&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 1 )
	{
		err = 1;
	}
	else if(x1 == 1 && x2 == 1  && x3 == 0&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 1 ) // 1110 1111
	{
		err = 2;
	}
	/*else if(x1 == 1 && x2 == 1  && x3 == 0&& x4 == 0 && x5 == 1 && x6 == 1  && x7 == 1 && x8 == 1) // 1100 1111
	{
		err = -2;
	}*/
	/*else if(x1 == 1 && x2 == 1  && x3 == 0&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 1 && x8 == 1) // 1101 1111
	{
		err = -2;
	}*/
	/*else if(x1 == 1 && x2 == 0  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 1 && x8 == 1) // 1011 1111
	{
		err = -3;
	}*/
	else if(x1 == 1 && x2 == 0  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 1)
	{
		err = 6;
	}
	else if(x1 == 1 && x2 == 0  && x3 == 0&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 1) // 1001 1111
	{
		err = 4;
	}
    
		else if(x1 == 0 && x2 == 0  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 1) // 0011 1111
	{
		err = 8;   //ע�ͣ�����ֱ�Ǵ��� Note, when treated as a right angle
	}
	else if(x1 == 0 && x2 == 1  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 1) // 0111 1111
	{
		err =12;
	}

	

	
	
	else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 0 && x5 == 0 && x6 == 1  && x7 == 1)
	{
		err = -1;
	}
	else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 1 && x5 == 0 && x6 == 1  && x7 == 1) // 1111 0111
	{
		err = -2;
	} 
	else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 1 && x5 == 0 && x6 == 0  && x7 == 1 ) // 1111 0011
	{
		err = -4;
	}
	/*else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 0  && x7 == 1 && x8 == 1) // 1111 1011
	{
		err = 2;
	}
	else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 0  && x7 == 0 && x8 == 1) // 1111 1001
	{
		err = 8;
	}*/
	
	else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 0  && x7 == 1) // 1111 1101
	{
		err = -6;
	}
	else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 0  && x7 == 0 ) // 1111 1100
	{
		err = -8; ///����ֱ�Ǵ���  treat as a right angle
	}
		else if(x1 == 1 && x2 == 1  && x3 == 1&& x4 == 1 && x5 == 1 && x6 == 1  && x7 == 0) // 1111 1110
	{
		err = -12;
	}
	

	
 
	else if(x1 == 1 &&x2 == 1 &&x3 == 1 && x4 == 0 && x5 == 1 && x6 == 1 && x7 == 1) //ֱ�� go straight
	{
		err = 0;
	}
	/*else if(x1 == 1 &&x2 == 1 &&x3 == 1 && x4 == 1 && x5 == 1 && x6 == 1 && x7 == 1&& x8 == 1)
	{
		err=-11;
	}*/


	/*else if(x1 == 1 &&x2 == 1 &&x3 == 1 && x4 == 1 && x5 == 1 && x6 == 1 && x7 == 1&& x8 == 1) //ֱ�� go straight
	{
		if(yaw<90&&yaw>-90)
		{
			if(yaw-0>0)
			{
				err=3;
			}
			else if(yaw-0<0)
			{
				err=-3;
			}
			else
			{
				err=0;
			}
		}
		if((yaw > 90 && yaw < 180) || (yaw > -180 && yaw < -90))
		{
			if(yaw > 90 && yaw < 180)
			{
			 if(yaw-180<0)
				{
				err=-3;
				}
			 else
				{
				err=0;
				}
			}
			else if(yaw > -180 && yaw < -90)
			{
			 if(yaw+180>0)
				{
				err=3;
				}
			 else
				{
				err=0;
				}
			}
		}
		
	}*///1111 1111
    

	//ʣ�µľͱ�����һ��״̬	    The rest will stay the same.
    
    
	pid_output_IRR = (int)(PID_IR_Calc(err));

	//Motion_Car_Control(IRR_SPEED, 0, pid_output_IRR);
	if(x1 == 1 &&x2 == 1 &&x3 == 1 && x4 == 1 && x5 == 1 && x6 == 1 && x7 == 1)
	{
		BZW=1;
		pid_output_IRR=70;
		Motor_RF_GO();
		Motor_RR_GO();
		Motor_LR_BACK();
		Motor_LF_BACK();
		PWM_SET_DutyLR(pid_output_IRR+200);
		PWM_SET_DutyLF(pid_output_IRR+200);
       	PWM_SET_DutyRF(pid_output_IRR+200);
        PWM_SET_DutyRR(pid_output_IRR+200);
	}
	else 
	{
		if(BZW==1)
		{
			BZW=0;
			runsign++;
			if(runsign==4)
			{
				runsign=0;
				stopsign++;
				if(stopsign>=quanshu)
				{
					Motor_Init();
				}
				else 
				{
					Motor_RF_GO();
					Motor_RR_GO();
					Motor_LR_GO();
					Motor_LF_GO();
					PWM_SET_DutyLR(pid_output_IRR+200);
					PWM_SET_DutyLF(pid_output_IRR+200);
       				PWM_SET_DutyRF(-pid_output_IRR+200);
        			PWM_SET_DutyRR(-pid_output_IRR+200);
				}
			}
		}
		else 
		{
			if(stopsign>=quanshu)
			{
				Motor_Init();
			}
			else 
			{
				Motor_RF_GO();
				Motor_RR_GO();
				Motor_LR_GO();
				Motor_LF_GO();
				PWM_SET_DutyLR(pid_output_IRR+200);
				PWM_SET_DutyLF(pid_output_IRR+200);
       			PWM_SET_DutyRF(-pid_output_IRR+200);
        		PWM_SET_DutyRR(-pid_output_IRR+200);
			}
			
		}
		/*Motor_RF_GO();
		Motor_RR_GO();
		Motor_LR_GO();
		Motor_LF_GO();
		PWM_SET_DutyLR(pid_output_IRR+200);
		PWM_SET_DutyLF(pid_output_IRR+200);
       	PWM_SET_DutyRF(-pid_output_IRR+200);
        PWM_SET_DutyRR(-pid_output_IRR+200);*/
	}


	/*if(pid_output_IRR==0)
		{
			Motor_RF_GO();
			Motor_RR_GO();
			Motor_LR_GO();
			Motor_LF_GO();
			PWM_SET_DutyLR(pid_output_IRR+200);
			PWM_SET_DutyLF(pid_output_IRR+200);
       		PWM_SET_DutyRF(pid_output_IRR+200);
        	PWM_SET_DutyRR(pid_output_IRR+200);
		}
		else if(pid_output_IRR<0)
		{
			pid_output_IRR=-pid_output_IRR;
			Motor_RF_GO();
			Motor_RR_GO();
			Motor_LR_BACK();
			Motor_LF_BACK();
			PWM_SET_DutyLR(pid_output_IRR+200);
			PWM_SET_DutyLF(pid_output_IRR+200);
        	PWM_SET_DutyRF(pid_output_IRR+200);
        	PWM_SET_DutyRR(pid_output_IRR+200);
		}
		else 
		{
			Motor_RF_BACK();
			Motor_RR_BACK();
			Motor_LR_GO();
			Motor_LF_GO();
			PWM_SET_DutyLR(pid_output_IRR+200);
			PWM_SET_DutyLF(pid_output_IRR+200);
        	PWM_SET_DutyRF(pid_output_IRR+200);
        	PWM_SET_DutyRR(pid_output_IRR+200);
		}*/
		
}
