******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 22:31:51 2025

OUTPUT FILE NAME:   <mspm0-modules.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00004531


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000059c8  0001a638  R  X
  SRAM                  20200000   00008000  0000040e  00007bf2  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000059c8   000059c8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004c00   00004c00    r-x .text
  00004cc0    00004cc0    00000cc8   00000cc8    r-- .rodata
  00005988    00005988    00000040   00000040    r-- .cinit
20200000    20200000    00000210   00000000    rw-
  20200000    20200000    000001b6   00000000    rw- .bss
  202001b8    202001b8    00000058   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004c00     
                  000000c0    00000364     libc.a : e_asin.c.obj (.text.asin)
                  00000424    000002f8            : s_atan.c.obj (.text.atan)
                  0000071c    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00000994    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  00000bcc    0000022c     mpu6050.o (.text.Read_Quad)
                  00000df8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001024    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001218    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000013b8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000154a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000154c    0000018c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000016d8    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  00001860    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  000019d8    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00001b48    00000144     mpu6050.o (.text.MPU6050_Init)
                  00001c8c    00000134     mspm0_i2c.o (.text.mspm0_i2c_read)
                  00001dc0    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  00001ef0    00000128     inv_mpu.o (.text.mpu_init)
                  00002018    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  0000213c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00002248    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00002350    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00002454    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00002554    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00002640    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00002728    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0000280c    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  000028f0    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000029c8    000000d4     inv_mpu.o (.text.set_int_enable)
                  00002a9c    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00002b6c    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  00002c30    000000c4     mspm0_i2c.o (.text.mspm0_i2c_write)
                  00002cf4    000000bc     main.o (.text.main)
                  00002db0    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00002e6c    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00002f24    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00002fd0    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  0000307c    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00003118    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000031b0    00000096     mpu6050.o (.text.inv_row_2_scale)
                  00003246    00000002     --HOLE-- [fill = 0]
                  00003248    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_LEFT_init)
                  000032d8    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_RIGHT_init)
                  00003368    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000033f4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003480    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  0000350c    00000084     clock.o (.text.__NVIC_SetPriority)
                  00003590    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00003614    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003696    00000002     --HOLE-- [fill = 0]
                  00003698    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003714    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00003788    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00003790    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00003804    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00003878    00000068     motor.o (.text.Motor_Init)
                  000038e0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00003948    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  000039ae    00000002     --HOLE-- [fill = 0]
                  000039b0    00000064     mspm0_i2c.o (.text.mpu6050_i2c_sda_unlock)
                  00003a14    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00003a76    00000002     --HOLE-- [fill = 0]
                  00003a78    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00003ad8    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00003b38    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00003b96    00000002     --HOLE-- [fill = 0]
                  00003b98    0000005c     mspm0_i2c.o (.text.mspm0_i2c_enable)
                  00003bf4    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00003c4c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003ca2    00000002     --HOLE-- [fill = 0]
                  00003ca4    00000050     mspm0_i2c.o (.text.DL_I2C_startControllerTransfer)
                  00003cf4    00000050     clock.o (.text.SysTick_Config)
                  00003d44    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00003d90    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003dd8    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00003e20    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  00003e68    00000048     mspm0_i2c.o (.text.mspm0_i2c_disable)
                  00003eb0    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00003ef4    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00003f38    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003f78    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003fb8    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003ff8    0000003e     mpu6050.o (.text.inv_orientation_matrix_to_scalar)
                  00004036    00000002     --HOLE-- [fill = 0]
                  00004038    0000003c     mspm0_i2c.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00004074    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  000040b0    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  000040ec    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004128    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004164    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000041a0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000041da    00000002     --HOLE-- [fill = 0]
                  000041dc    00000038     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004214    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0000424c    00000034     mspm0_i2c.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004280    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000042b4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000042e8    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  0000431c    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  0000434e    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00004380    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000043b0    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  000043e0    00000030     clock.o (.text.mspm0_delay_ms)
                  00004410    0000002c     interrupt.o (.text.__NVIC_EnableIRQ)
                  0000443c    0000002c     clock.o (.text.mspm0_get_clock_ms)
                  00004468    00000028     mspm0_i2c.o (.text.DL_Common_updateReg)
                  00004490    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000044b8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000044e0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00004508    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00004530    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004558    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  0000457e    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000045a4    00000026     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000045ca    00000002     --HOLE-- [fill = 0]
                  000045cc    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000045ec    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  0000460c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000462a    00000002     --HOLE-- [fill = 0]
                  0000462c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00004648    0000001c     mspm0_i2c.o (.text.DL_GPIO_enableHiZ)
                  00004664    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00004680    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000469c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000046b8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000046d4    0000001c     mpu6050.o (.text.DL_I2C_getSDAStatus)
                  000046f0    0000001c     mspm0_i2c.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  0000470c    0000001c     interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00004728    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00004744    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00004760    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000477c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00004798    0000001c     interrupt.o (.text.Interrupt_Init)
                  000047b4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000047d0    00000018     mspm0_i2c.o (.text.DL_GPIO_enableOutput)
                  000047e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00004800    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00004818    00000018     mspm0_i2c.o (.text.DL_GPIO_initDigitalOutput)
                  00004830    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00004848    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00004860    00000018     mspm0_i2c.o (.text.DL_GPIO_setPins)
                  00004878    00000018     mspm0_i2c.o (.text.DL_I2C_clearInterruptStatus)
                  00004890    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000048a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000048c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000048d8    00000018     mspm0_i2c.o (.text.DL_I2C_enablePower)
                  000048f0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00004908    00000018     mspm0_i2c.o (.text.DL_I2C_getRawInterruptStatus)
                  00004920    00000018     mspm0_i2c.o (.text.DL_I2C_reset)
                  00004938    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00004950    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00004968    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00004980    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00004998    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000049b0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000049c8    00000018     pwm.o (.text.DL_Timer_startCounter)
                  000049e0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000049f8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004a10    00000018     pwm.o (.text.PWM_SET_Init)
                  00004a28    00000016     main.o (.text.DL_GPIO_readPins)
                  00004a3e    00000016     mspm0_i2c.o (.text.DL_GPIO_readPins)
                  00004a54    00000016     mspm0_i2c.o (.text.DL_I2C_transmitControllerData)
                  00004a6a    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00004a80    00000016     interrupt.o (.text.GROUP1_IRQHandler)
                  00004a96    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00004aac    00000014     motor.o (.text.DL_GPIO_clearPins)
                  00004ac0    00000014     mspm0_i2c.o (.text.DL_GPIO_clearPins)
                  00004ad4    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004ae8    00000014     mspm0_i2c.o (.text.DL_I2C_getControllerStatus)
                  00004afc    00000014     mspm0_i2c.o (.text.DL_I2C_receiveControllerData)
                  00004b10    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00004b24    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004b38    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004b4c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004b60    00000014     clock.o (.text.SysTick_Init)
                  00004b74    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00004b88    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00004b9c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00004bae    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004bc0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004bd2    00000002     --HOLE-- [fill = 0]
                  00004bd4    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004be4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004bf4    00000010     interrupt.o (.text.SysTick_Handler)
                  00004c04    0000000e     mpu6050.o (.text.tap_cb)
                  00004c12    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004c1c    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004c26    00000002     --HOLE-- [fill = 0]
                  00004c28    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00004c38    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004c42    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004c4c    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00004c56    00000002     --HOLE-- [fill = 0]
                  00004c58    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00004c68    0000000a     mpu6050.o (.text.android_orient_cb)
                  00004c72    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00004c7a    00000002     --HOLE-- [fill = 0]
                  00004c7c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004c84    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004c8a    00000002     --HOLE-- [fill = 0]
                  00004c8c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00004c9c    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00004ca2    00000006            : exit.c.obj (.text:abort)
                  00004ca8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004cac    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004cb0    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00004cb4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004cb8    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00004cbc    00000004     --HOLE-- [fill = 0]

.cinit     0    00005988    00000040     
                  00005988    0000001a     (.cinit..data.load) [load image, compression = lzss]
                  000059a2    00000002     --HOLE-- [fill = 0]
                  000059a4    0000000c     (__TI_handler_table)
                  000059b0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000059b8    00000010     (__TI_cinit_table)

.rodata    0    00004cc0    00000cc8     
                  00004cc0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000058b6    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  000058b8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  000058f8    00000028     inv_mpu.o (.rodata.test)
                  00005920    0000001e     inv_mpu.o (.rodata.reg)
                  0000593e    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00005940    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00005954    0000000c     inv_mpu.o (.rodata.hw)
                  00005960    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000596a    00000002     --HOLE-- [fill = 0]
                  0000596c    00000008     ti_msp_dl_config.o (.rodata.gPWM_LEFTConfig)
                  00005974    00000008     ti_msp_dl_config.o (.rodata.gPWM_RIGHTConfig)
                  0000597c    00000003     ti_msp_dl_config.o (.rodata.gPWM_LEFTClockConfig)
                  0000597f    00000003     ti_msp_dl_config.o (.rodata.gPWM_RIGHTClockConfig)
                  00005982    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00005985    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001b6     UNINITIALIZED
                  20200000    000000bc     (.common:gPWM_RIGHTBackup)
                  202000bc    000000bc     (.common:gTIMER_0Backup)
                  20200178    00000010     (.common:quat)
                  20200188    00000006     (.common:accel)
                  2020018e    00000006     (.common:gyro)
                  20200194    00000004     (.common:pitch)
                  20200198    00000004     (.common:roll)
                  2020019c    00000004     (.common:sensor_timestamp)
                  202001a0    00000004     (.common:start_time)
                  202001a4    00000004     (.common:tick_ms)
                  202001a8    00000004     (.common:yaw)
                  202001ac    00000002     (.common:sensors)
                  202001ae    00000001     (.common:more)
                  202001af    00000001     (.common:x1)
                  202001b0    00000001     (.common:x2)
                  202001b1    00000001     (.common:x3)
                  202001b2    00000001     (.common:x4)
                  202001b3    00000001     (.common:x5)
                  202001b4    00000001     (.common:x6)
                  202001b5    00000001     (.common:x7)

.data      0    202001b8    00000058     UNINITIALIZED
                  202001b8    0000002c     inv_mpu.o (.data.st)
                  202001e4    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202001f4    0000000e     mpu6050.o (.data.hal)
                  20200202    00000009     mpu6050.o (.data.gyro_orientation)
                  2020020b    00000001     interrupt.o (.data.enable_group1_irq)
                  2020020c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2416    59        376    
       main.o                         210     0         0      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2634    251       376    
                                                               
    .\Drivers\MPU6050\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
       mspm0_i2c.o                    1328    0         0      
       mpu6050.o                      1144    0         70     
    +--+------------------------------+-------+---------+---------+
       Total:                         10182   3144      130    
                                                               
    .\Drivers\MSPM0\
       clock.o                        324     0         8      
       interrupt.o                    138     0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         462     0         9      
                                                               
    .\EIGHT_TRACKING\
       eight_tracking.o               0       0         7      
    +--+------------------------------+-------+---------+---------+
       Total:                         0       0         7      
                                                               
    .\MOTOR\
       motor.o                        124     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         124     0         0      
                                                               
    .\PWM\
       pwm.o                          48      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         48      0         0      
                                                               
    C:/ti/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         880     0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memcmp.c.obj                   32      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2784    64        4      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    C:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatunsisf.S.obj              40      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2304    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       62        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   19422   3521      1038   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000059b8 records: 2, size/record: 8, table size: 16
	.data: load addr=00005988, load size=0000001a bytes, run addr=202001b8, run size=00000058 bytes, compression=lzss
	.bss: load addr=000059b0, load size=00000008 bytes, run addr=20200000, run size=000001b6 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000059a4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00002729     00004c28     00004c24   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004c40          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004c4a          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00004c78          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00004ca0          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   0000213d     00004c58     00004c54   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000013c3     00004c8c     00004c88   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00004cb2          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)

[3 trampolines]
[8 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00004ca9  ADC0_IRQHandler                 
00004ca9  ADC1_IRQHandler                 
00004ca9  AES_IRQHandler                  
00004cac  C$$EXIT                         
00004ca9  CANFD0_IRQHandler               
00004ca9  DAC0_IRQHandler                 
00004c13  DL_Common_delayCycles           
00003b39  DL_I2C_fillControllerTXFIFO     
000040b1  DL_I2C_flushControllerTXFIFO    
0000457f  DL_I2C_setClockConfig           
00002351  DL_Timer_initFourCCPWMMode      
00002641  DL_Timer_initTimerMode          
00004761  DL_Timer_setCaptCompUpdateMethod
000049b1  DL_Timer_setCaptureCompareOutCtl
00004be5  DL_Timer_setCaptureCompareValue 
0000477d  DL_Timer_setClockConfig         
00003d91  DL_UART_init                    
00004b9d  DL_UART_setClockConfig          
00004ca9  DMA_IRQHandler                  
00004ca9  Default_Handler                 
00004ca9  GROUP0_IRQHandler               
00004a81  GROUP1_IRQHandler               
00004cad  HOSTexit                        
00004ca9  HardFault_Handler               
00004ca9  I2C0_IRQHandler                 
00004ca9  I2C1_IRQHandler                 
00004799  Interrupt_Init                  
00001b49  MPU6050_Init                    
00003879  Motor_Init                      
00004ca9  NMI_Handler                     
00004a11  PWM_SET_Init                    
00004ca9  PendSV_Handler                  
00004ca9  RTC_IRQHandler                  
00000bcd  Read_Quad                       
00004cb5  Reset_Handler                   
00004ca9  SPI0_IRQHandler                 
00004ca9  SPI1_IRQHandler                 
00004ca9  SVC_Handler                     
0000154d  SYSCFG_DL_GPIO_init             
00003bf5  SYSCFG_DL_I2C_MPU6050_init      
00003249  SYSCFG_DL_PWM_LEFT_init         
000032d9  SYSCFG_DL_PWM_RIGHT_init        
000045a5  SYSCFG_DL_SYSCTL_init           
000042b5  SYSCFG_DL_TIMER_0_init          
00003f39  SYSCFG_DL_UART_0_init           
000041dd  SYSCFG_DL_init                  
00003369  SYSCFG_DL_initPower             
00004bf5  SysTick_Handler                 
00004b61  SysTick_Init                    
00004ca9  TIMA0_IRQHandler                
00004ca9  TIMA1_IRQHandler                
00004ca9  TIMG0_IRQHandler                
00004ca9  TIMG12_IRQHandler               
00004ca9  TIMG6_IRQHandler                
00004ca9  TIMG7_IRQHandler                
00004ca9  TIMG8_IRQHandler                
00004baf  TI_memcpy_small                 
00004ca9  UART0_IRQHandler                
00004ca9  UART1_IRQHandler                
00004ca9  UART2_IRQHandler                
00004ca9  UART3_IRQHandler                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
000059b8  __TI_CINIT_Base                 
000059c8  __TI_CINIT_Limit                
000059c8  __TI_CINIT_Warm                 
000059a4  __TI_Handler_Table_Base         
000059b0  __TI_Handler_Table_Limit        
00004165  __TI_auto_init_nobinit_nopinit  
00003699  __TI_decompress_lzss            
00004bc1  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00004a97  __TI_zero_init_nomemset         
000013c3  __adddf3                        
000028fb  __addsf3                        
00003791  __aeabi_d2f                     
000013c3  __aeabi_dadd                    
00003a15  __aeabi_dcmpeq                  
00003a51  __aeabi_dcmpge                  
00003a65  __aeabi_dcmpgt                  
00003a3d  __aeabi_dcmple                  
00003a29  __aeabi_dcmplt                  
0000213d  __aeabi_ddiv                    
00002729  __aeabi_dmul                    
000013b9  __aeabi_dsub                    
2020020c  __aeabi_errno                   
00003789  __aeabi_errno_addr              
00003fb9  __aeabi_f2d                     
00004215  __aeabi_f2iz                    
000028fb  __aeabi_fadd                    
00003615  __aeabi_fdiv                    
000033f5  __aeabi_fmul                    
000028f1  __aeabi_fsub                    
00004129  __aeabi_i2f                     
00003c4d  __aeabi_idiv                    
0000154b  __aeabi_idiv0                   
00003c4d  __aeabi_idivmod                 
00004c7d  __aeabi_memcpy                  
00004c7d  __aeabi_memcpy4                 
00004c7d  __aeabi_memcpy8                 
00004509  __aeabi_ui2f                    
00003f79  __aeabi_uidiv                   
00003f79  __aeabi_uidivmod                
ffffffff  __binit__                       
000038e1  __cmpdf2                        
0000213d  __divdf3                        
00003615  __divsf3                        
000038e1  __eqdf2                         
00003fb9  __extendsfdf2                   
00004215  __fixsfsi                       
00004129  __floatsisf                     
00004509  __floatunsisf                   
00003715  __gedf2                         
00003715  __gtdf2                         
000038e1  __ledf2                         
000038e1  __ltdf2                         
UNDEFED   __mpu_init                      
00002729  __muldf3                        
000041a1  __muldsi3                       
000033f5  __mulsf3                        
000038e1  __nedf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000013b9  __subdf3                        
000028f1  __subsf3                        
00003791  __truncdfsf2                    
00004531  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00004cb9  _system_pre_init                
00004ca3  abort                           
20200188  accel                           
000000c1  asin                            
000000c1  asinl                           
00000425  atan                            
000016d9  atan2                           
000016d9  atan2l                          
00000425  atanl                           
ffffffff  binit                           
00003dd9  dmp_enable_6x_lp_quat           
0000071d  dmp_enable_feature              
00003a79  dmp_enable_gyro_cal             
00003e21  dmp_enable_lp_quat              
000047b5  dmp_load_motion_driver_firmware 
00001025  dmp_read_fifo                   
00004b75  dmp_register_android_orient_cb  
00004b89  dmp_register_tap_cb             
00003119  dmp_set_fifo_rate               
00001861  dmp_set_orientation             
00003eb1  dmp_set_shake_reject_thresh     
0000431d  dmp_set_shake_reject_time       
0000434f  dmp_set_shake_reject_timeout    
00003949  dmp_set_tap_axes                
00003ef5  dmp_set_tap_count               
00000995  dmp_set_tap_thresh              
00004381  dmp_set_tap_time                
000043b1  dmp_set_tap_time_multi          
2020020b  enable_group1_irq               
20200000  gPWM_RIGHTBackup                
202000bc  gTIMER_0Backup                  
2020018e  gyro                            
00005954  hw                              
00000000  interruptVectors                
00002cf5  main                            
000045ed  memcmp                          
202001ae  more                            
000039b1  mpu6050_i2c_sda_unlock          
00002db1  mpu_configure_fifo              
00003805  mpu_get_accel_fsr               
00003ad9  mpu_get_gyro_fsr                
000042e9  mpu_get_sample_rate             
00001ef1  mpu_init                        
00002019  mpu_load_firmware               
00002455  mpu_lp_accel_mode               
00002249  mpu_read_fifo_stream            
00002f25  mpu_read_mem                    
00000df9  mpu_reset_fifo                  
0000280d  mpu_set_accel_fsr               
00001219  mpu_set_bypass                  
00002e6d  mpu_set_dmp_state               
00002b6d  mpu_set_gyro_fsr                
0000307d  mpu_set_int_latched             
00002a9d  mpu_set_lpf                     
00002555  mpu_set_sample_rate             
00001dc1  mpu_set_sensors                 
00002fd1  mpu_write_mem                   
000043e1  mspm0_delay_ms                  
0000443d  mspm0_get_clock_ms              
00001c8d  mspm0_i2c_read                  
00002c31  mspm0_i2c_write                 
20200194  pitch                           
20200178  quat                            
00005920  reg                             
20200198  roll                            
2020019c  sensor_timestamp                
202001ac  sensors                         
000019d9  sqrt                            
000019d9  sqrtl                           
202001a0  start_time                      
000058f8  test                            
202001a4  tick_ms                         
202001af  x1                              
202001b0  x2                              
202001b1  x3                              
202001b2  x4                              
202001b3  x5                              
202001b4  x6                              
202001b5  x7                              
202001a8  yaw                             


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  asin                            
000000c1  asinl                           
00000200  __STACK_SIZE                    
00000425  atan                            
00000425  atanl                           
0000071d  dmp_enable_feature              
00000995  dmp_set_tap_thresh              
00000bcd  Read_Quad                       
00000df9  mpu_reset_fifo                  
00001025  dmp_read_fifo                   
00001219  mpu_set_bypass                  
000013b9  __aeabi_dsub                    
000013b9  __subdf3                        
000013c3  __adddf3                        
000013c3  __aeabi_dadd                    
0000154b  __aeabi_idiv0                   
0000154d  SYSCFG_DL_GPIO_init             
000016d9  atan2                           
000016d9  atan2l                          
00001861  dmp_set_orientation             
000019d9  sqrt                            
000019d9  sqrtl                           
00001b49  MPU6050_Init                    
00001c8d  mspm0_i2c_read                  
00001dc1  mpu_set_sensors                 
00001ef1  mpu_init                        
00002019  mpu_load_firmware               
0000213d  __aeabi_ddiv                    
0000213d  __divdf3                        
00002249  mpu_read_fifo_stream            
00002351  DL_Timer_initFourCCPWMMode      
00002455  mpu_lp_accel_mode               
00002555  mpu_set_sample_rate             
00002641  DL_Timer_initTimerMode          
00002729  __aeabi_dmul                    
00002729  __muldf3                        
0000280d  mpu_set_accel_fsr               
000028f1  __aeabi_fsub                    
000028f1  __subsf3                        
000028fb  __addsf3                        
000028fb  __aeabi_fadd                    
00002a9d  mpu_set_lpf                     
00002b6d  mpu_set_gyro_fsr                
00002c31  mspm0_i2c_write                 
00002cf5  main                            
00002db1  mpu_configure_fifo              
00002e6d  mpu_set_dmp_state               
00002f25  mpu_read_mem                    
00002fd1  mpu_write_mem                   
0000307d  mpu_set_int_latched             
00003119  dmp_set_fifo_rate               
00003249  SYSCFG_DL_PWM_LEFT_init         
000032d9  SYSCFG_DL_PWM_RIGHT_init        
00003369  SYSCFG_DL_initPower             
000033f5  __aeabi_fmul                    
000033f5  __mulsf3                        
00003615  __aeabi_fdiv                    
00003615  __divsf3                        
00003699  __TI_decompress_lzss            
00003715  __gedf2                         
00003715  __gtdf2                         
00003789  __aeabi_errno_addr              
00003791  __aeabi_d2f                     
00003791  __truncdfsf2                    
00003805  mpu_get_accel_fsr               
00003879  Motor_Init                      
000038e1  __cmpdf2                        
000038e1  __eqdf2                         
000038e1  __ledf2                         
000038e1  __ltdf2                         
000038e1  __nedf2                         
00003949  dmp_set_tap_axes                
000039b1  mpu6050_i2c_sda_unlock          
00003a15  __aeabi_dcmpeq                  
00003a29  __aeabi_dcmplt                  
00003a3d  __aeabi_dcmple                  
00003a51  __aeabi_dcmpge                  
00003a65  __aeabi_dcmpgt                  
00003a79  dmp_enable_gyro_cal             
00003ad9  mpu_get_gyro_fsr                
00003b39  DL_I2C_fillControllerTXFIFO     
00003bf5  SYSCFG_DL_I2C_MPU6050_init      
00003c4d  __aeabi_idiv                    
00003c4d  __aeabi_idivmod                 
00003d91  DL_UART_init                    
00003dd9  dmp_enable_6x_lp_quat           
00003e21  dmp_enable_lp_quat              
00003eb1  dmp_set_shake_reject_thresh     
00003ef5  dmp_set_tap_count               
00003f39  SYSCFG_DL_UART_0_init           
00003f79  __aeabi_uidiv                   
00003f79  __aeabi_uidivmod                
00003fb9  __aeabi_f2d                     
00003fb9  __extendsfdf2                   
000040b1  DL_I2C_flushControllerTXFIFO    
00004129  __aeabi_i2f                     
00004129  __floatsisf                     
00004165  __TI_auto_init_nobinit_nopinit  
000041a1  __muldsi3                       
000041dd  SYSCFG_DL_init                  
00004215  __aeabi_f2iz                    
00004215  __fixsfsi                       
000042b5  SYSCFG_DL_TIMER_0_init          
000042e9  mpu_get_sample_rate             
0000431d  dmp_set_shake_reject_time       
0000434f  dmp_set_shake_reject_timeout    
00004381  dmp_set_tap_time                
000043b1  dmp_set_tap_time_multi          
000043e1  mspm0_delay_ms                  
0000443d  mspm0_get_clock_ms              
00004509  __aeabi_ui2f                    
00004509  __floatunsisf                   
00004531  _c_int00_noargs                 
0000457f  DL_I2C_setClockConfig           
000045a5  SYSCFG_DL_SYSCTL_init           
000045ed  memcmp                          
00004761  DL_Timer_setCaptCompUpdateMethod
0000477d  DL_Timer_setClockConfig         
00004799  Interrupt_Init                  
000047b5  dmp_load_motion_driver_firmware 
000049b1  DL_Timer_setCaptureCompareOutCtl
00004a11  PWM_SET_Init                    
00004a81  GROUP1_IRQHandler               
00004a97  __TI_zero_init_nomemset         
00004b61  SysTick_Init                    
00004b75  dmp_register_android_orient_cb  
00004b89  dmp_register_tap_cb             
00004b9d  DL_UART_setClockConfig          
00004baf  TI_memcpy_small                 
00004bc1  __TI_decompress_none            
00004be5  DL_Timer_setCaptureCompareValue 
00004bf5  SysTick_Handler                 
00004c13  DL_Common_delayCycles           
00004c7d  __aeabi_memcpy                  
00004c7d  __aeabi_memcpy4                 
00004c7d  __aeabi_memcpy8                 
00004ca3  abort                           
00004ca9  ADC0_IRQHandler                 
00004ca9  ADC1_IRQHandler                 
00004ca9  AES_IRQHandler                  
00004ca9  CANFD0_IRQHandler               
00004ca9  DAC0_IRQHandler                 
00004ca9  DMA_IRQHandler                  
00004ca9  Default_Handler                 
00004ca9  GROUP0_IRQHandler               
00004ca9  HardFault_Handler               
00004ca9  I2C0_IRQHandler                 
00004ca9  I2C1_IRQHandler                 
00004ca9  NMI_Handler                     
00004ca9  PendSV_Handler                  
00004ca9  RTC_IRQHandler                  
00004ca9  SPI0_IRQHandler                 
00004ca9  SPI1_IRQHandler                 
00004ca9  SVC_Handler                     
00004ca9  TIMA0_IRQHandler                
00004ca9  TIMA1_IRQHandler                
00004ca9  TIMG0_IRQHandler                
00004ca9  TIMG12_IRQHandler               
00004ca9  TIMG6_IRQHandler                
00004ca9  TIMG7_IRQHandler                
00004ca9  TIMG8_IRQHandler                
00004ca9  UART0_IRQHandler                
00004ca9  UART1_IRQHandler                
00004ca9  UART2_IRQHandler                
00004ca9  UART3_IRQHandler                
00004cac  C$$EXIT                         
00004cad  HOSTexit                        
00004cb5  Reset_Handler                   
00004cb9  _system_pre_init                
000058f8  test                            
00005920  reg                             
00005954  hw                              
000059a4  __TI_Handler_Table_Base         
000059b0  __TI_Handler_Table_Limit        
000059b8  __TI_CINIT_Base                 
000059c8  __TI_CINIT_Limit                
000059c8  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_RIGHTBackup                
202000bc  gTIMER_0Backup                  
20200178  quat                            
20200188  accel                           
2020018e  gyro                            
20200194  pitch                           
20200198  roll                            
2020019c  sensor_timestamp                
202001a0  start_time                      
202001a4  tick_ms                         
202001a8  yaw                             
202001ac  sensors                         
202001ae  more                            
202001af  x1                              
202001b0  x2                              
202001b1  x3                              
202001b2  x4                              
202001b3  x5                              
202001b4  x6                              
202001b5  x7                              
2020020b  enable_group1_irq               
2020020c  __aeabi_errno                   
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[223 symbols]
