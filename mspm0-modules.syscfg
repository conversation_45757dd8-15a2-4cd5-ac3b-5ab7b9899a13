/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const I2C    = scripting.addModule("/ti/driverlib/I2C", {}, false);
const I2C1   = I2C.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const PWM2   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
GPIO1.$name                               = "GPIO_MPU6050";
GPIO1.associatedPins[0].$name             = "PIN_INT";
GPIO1.associatedPins[0].direction         = "INPUT";
GPIO1.associatedPins[0].internalResistor  = "PULL_UP";
GPIO1.associatedPins[0].interruptEn       = true;
GPIO1.associatedPins[0].interruptPriority = "1";
GPIO1.associatedPins[0].polarity          = "FALL";
GPIO1.associatedPins[0].pin.$assign       = "PB1";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO2.$name                          = "GPIO_IN";
GPIO2.associatedPins.create(8);
GPIO2.associatedPins[0].$name        = "PIN_INA1";
GPIO2.associatedPins[0].assignedPort = "PORTB";
GPIO2.associatedPins[0].assignedPin  = "15";
GPIO2.associatedPins[1].assignedPort = "PORTB";
GPIO2.associatedPins[1].assignedPin  = "16";
GPIO2.associatedPins[1].$name        = "PIN_INA2";
GPIO2.associatedPins[2].$name        = "PIN_INB1";
GPIO2.associatedPins[2].assignedPort = "PORTA";
GPIO2.associatedPins[2].assignedPin  = "27";
GPIO2.associatedPins[3].$name        = "PIN_INB2";
GPIO2.associatedPins[3].assignedPort = "PORTB";
GPIO2.associatedPins[3].assignedPin  = "19";
GPIO2.associatedPins[4].$name        = "PIN_INC1";
GPIO2.associatedPins[4].assignedPort = "PORTA";
GPIO2.associatedPins[4].assignedPin  = "22";
GPIO2.associatedPins[5].$name        = "PIN_INC2";
GPIO2.associatedPins[5].assignedPort = "PORTB";
GPIO2.associatedPins[5].assignedPin  = "18";
GPIO2.associatedPins[6].$name        = "PIN_IND1";
GPIO2.associatedPins[6].assignedPort = "PORTA";
GPIO2.associatedPins[6].assignedPin  = "18";
GPIO2.associatedPins[7].$name        = "PIN_IND2";
GPIO2.associatedPins[7].assignedPort = "PORTA";
GPIO2.associatedPins[7].assignedPin  = "24";

GPIO3.$name                          = "GPIO_GRP_huidu";
GPIO3.associatedPins.create(7);
GPIO3.associatedPins[0].$name        = "OUT1";
GPIO3.associatedPins[0].assignedPort = "PORTA";
GPIO3.associatedPins[0].direction    = "INPUT";
GPIO3.associatedPins[0].pin.$assign  = "PA17";
GPIO3.associatedPins[1].$name        = "OUT2";
GPIO3.associatedPins[1].direction    = "INPUT";
GPIO3.associatedPins[1].assignedPort = "PORTA";
GPIO3.associatedPins[1].pin.$assign  = "PA15";
GPIO3.associatedPins[2].$name        = "OUT3";
GPIO3.associatedPins[2].direction    = "INPUT";
GPIO3.associatedPins[2].assignedPort = "PORTA";
GPIO3.associatedPins[2].pin.$assign  = "PA12";
GPIO3.associatedPins[3].$name        = "OUT4";
GPIO3.associatedPins[3].direction    = "INPUT";
GPIO3.associatedPins[3].assignedPort = "PORTA";
GPIO3.associatedPins[4].$name        = "OUT5";
GPIO3.associatedPins[4].direction    = "INPUT";
GPIO3.associatedPins[4].assignedPort = "PORTB";
GPIO3.associatedPins[4].pin.$assign  = "PB8";
GPIO3.associatedPins[5].$name        = "OUT6";
GPIO3.associatedPins[5].direction    = "INPUT";
GPIO3.associatedPins[5].assignedPort = "PORTB";
GPIO3.associatedPins[5].pin.$assign  = "PB7";
GPIO3.associatedPins[6].$name        = "OUT7";
GPIO3.associatedPins[6].assignedPort = "PORTB";
GPIO3.associatedPins[6].pin.$assign  = "PB17";

I2C1.$name                             = "I2C_MPU6050";
I2C1.basicEnableController             = true;
I2C1.basicControllerStandardBusSpeed   = "Fast";
I2C1.peripheral.sdaPin.$assign         = "PA28";
I2C1.peripheral.sclPin.$assign         = "PA31";
I2C1.sdaPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sdaPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sdaPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sdaPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
I2C1.sclPinConfig.hideOutputInversion  = scripting.forceWrite(false);
I2C1.sclPinConfig.onlyInternalResistor = scripting.forceWrite(false);
I2C1.sclPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
I2C1.sclPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

PWM1.$name                              = "PWM_RIGHT";
PWM1.timerCount                         = 320;
PWM1.peripheral.$assign                 = "TIMA1";
PWM1.peripheral.ccp0Pin.$assign         = "PB4";
PWM1.peripheral.ccp1Pin.$assign         = "PA16";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";

PWM2.$name                              = "PWM_LEFT";
PWM2.timerCount                         = 320;
PWM2.peripheral.$assign                 = "TIMG12";
PWM2.peripheral.ccp0Pin.$assign         = "PB13";
PWM2.peripheral.ccp1Pin.$assign         = "PB24";
PWM2.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC2";
PWM2.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC3";
PWM2.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric6";
PWM2.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM2.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM2.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM2.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM2.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric7";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

TIMER1.$name              = "TIMER_0";
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerClkPrescale   = 256;
TIMER1.timerPeriod        = "10ms";
TIMER1.timerMode          = "PERIODIC_UP_DOWN";
TIMER1.peripheral.$assign = "TIMA0";

UART1.$name                    = "UART_0";
UART1.peripheral.rxPin.$assign = "PA9";
UART1.peripheral.txPin.$assign = "PA8";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric8";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric9";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
Board.peripheral.$suggestSolution            = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution   = "PA20";
Board.peripheral.swdioPin.$suggestSolution   = "PA19";
GPIO2.associatedPins[0].pin.$suggestSolution = "PB15";
GPIO2.associatedPins[1].pin.$suggestSolution = "PB16";
GPIO2.associatedPins[2].pin.$suggestSolution = "PA27";
GPIO2.associatedPins[3].pin.$suggestSolution = "PB19";
GPIO2.associatedPins[4].pin.$suggestSolution = "PA22";
GPIO2.associatedPins[5].pin.$suggestSolution = "PB18";
GPIO2.associatedPins[6].pin.$suggestSolution = "PA18";
GPIO2.associatedPins[7].pin.$suggestSolution = "PA24";
GPIO3.associatedPins[3].pin.$suggestSolution = "PA13";
I2C1.peripheral.$suggestSolution             = "I2C0";
UART1.peripheral.$suggestSolution            = "UART1";
