/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "ti_msp_dl_config.h"
#include "main.h"
#include "stdio.h"
#include "pwm.h"
#include "motor.h"
#include "interrupt.h"
#include "eight_tracking.h"

//uint8_t oled_buffer[32];
int32_t SPEED_LF,SPEED_LR,SPEED_RF,SPEED_RR;

int main(void)
{
    SYSCFG_DL_init();
    SysTick_Init();
    PWM_SET_Init();
    Motor_Init();

    MPU6050_Init();
    //OLED_Init();

    /* Don't remove this! */
    Interrupt_Init();

    while (1) 
    {
        x1 = DL_GPIO_readPins(GPIO_GRP_huidu_OUT1_PORT,GPIO_GRP_huidu_OUT1_PIN);
        x2 = DL_GPIO_readPins(GPIO_GRP_huidu_OUT2_PORT,GPIO_GRP_huidu_OUT2_PIN);
        x3 = DL_GPIO_readPins(GPIO_GRP_huidu_OUT3_PORT,GPIO_GRP_huidu_OUT3_PIN);
        x4 = DL_GPIO_readPins(GPIO_GRP_huidu_OUT4_PORT,GPIO_GRP_huidu_OUT4_PIN);
        x5 = DL_GPIO_readPins(GPIO_GRP_huidu_OUT5_PORT,GPIO_GRP_huidu_OUT5_PIN);
        x6 = DL_GPIO_readPins(GPIO_GRP_huidu_OUT6_PORT,GPIO_GRP_huidu_OUT6_PIN);
        x7 = DL_GPIO_readPins(GPIO_GRP_huidu_OUT7_PORT,GPIO_GRP_huidu_OUT7_PIN);
        //LineWalking();-
    }
}